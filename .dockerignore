# IntelliJ IDE
/.idea

# System
.DS_Store

# Local Docker Compose overrides
/compose.override.yml

# Local ENV overrides
/.env.local
/env/*.env
!/env/.local.*.env


# This file contains .gitignore rules that are specific to the structure of the
# Drupal recommended-project Composer template. Because .gitignore is specific
# to your site and its deployment processes, you may need to uncomment, add, or
# remove rules.


# Ignore paths that contain user-generated content.
/drupal/web/sites/*/files
/drupal/web/sites/*/private

# Ignore SimpleTest multi-site environment.
/drupal/web/sites/simpletest


# Ignore directories generated by Composer
#
# See the "installer-paths" section in the top-level composer.json
# file.
# ------------------------------------------------------------------
/drush/Commands/contrib/
/drupal/web/composer/
/drupal/web/core/
/drupal/web/modules/contrib/
/drupal/web/themes/contrib/
/drupal/web/profiles/contrib/
/drupal/web/libraries/

# Generally you should only ignore the root vendor directory. It's important
# that core/assets/vendor and any other vendor directories within contrib or
# custom module, theme, etc., are not ignored unless you purposely do so.
/vendor/

# Ignore scaffold files
#
# Note that the scaffold plugin may be used to automatically manage
# a site's .gitignore files. If the `vendor` directory is ignored,
# then one or more .gitignore files will be written to also ignore
# any file placed by scaffolding. To avoid the creation of
# additional .gitignore files, add all of the scaffold file
# locations to the top-level .gitignore file, as shown below.
# ------------------------------------------------------------------
/drupal/web/.csslintrc
/drupal/web/.eslintignore
/drupal/web/.eslintrc.json
/drupal/web/.ht.router.php
/drupal/web/.htaccess
/drupal/web/INSTALL.txt
/drupal/web/README.md
/drupal/web/autoload.php
/drupal/web/example.gitignore
/drupal/web/index.php
/drupal/web/robots.txt
/drupal/web/update.php
/drupal/web/web.config
/drupal/web/modules/README.txt
/drupal/web/profiles/README.txt
/drupal/web/sites/README.txt
/drupal/web/sites/default/default.services.yml
/drupal/web/sites/default/default.settings.php
/drupal/web/sites/development.services.yml
/drupal/web/sites/example.settings.local.php
/drupal/web/sites/example.sites.php
/drupal/web/themes/README.txt
