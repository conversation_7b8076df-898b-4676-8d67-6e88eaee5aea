# This file contains .gitignore rules that are specific to the structure of the
# Drupal recommended-project Composer template. Because .gitignore is specific
# to your site and its deployment processes, you may need to uncomment, add, or
# remove rules.


# Ignore paths that contain user-generated content.
/web/sites/*/files
/web/sites/*/private

# Ignore SimpleTest multi-site environment.
/web/sites/simpletest


# Ignore directories generated by Composer
#
# See the "installer-paths" section in the top-level composer.json
# file.
# ------------------------------------------------------------------
/drush/Commands/contrib/
/web/composer/
/web/core/
/web/modules/contrib/
/web/themes/contrib/
/web/profiles/contrib/
/web/libraries/

# Generally you should only ignore the root vendor directory. It's important
# that core/assets/vendor and any other vendor directories within contrib or
# custom module, theme, etc., are not ignored unless you purposely do so.
/vendor/

# Ignore scaffold files
#
# Note that the scaffold plugin may be used to automatically manage
# a site's .gitignore files. If the `vendor` directory is ignored,
# then one or more .gitignore files will be written to also ignore
# any file placed by scaffolding. To avoid the creation of
# additional .gitignore files, add all of the scaffold file
# locations to the top-level .gitignore file, as shown below.
# ------------------------------------------------------------------
/.editorconfig
/.gitattributes
/web/.csslintrc
/web/.eslintignore
/web/.eslintrc.json
/web/.ht.router.php
/web/.htaccess
/web/INSTALL.txt
/web/README.md
/web/autoload.php
/web/example.gitignore
/web/index.php
/web/robots.txt
/web/update.php
/web/web.config
/web/modules/README.txt
/web/profiles/README.txt
/web/sites/README.txt
/web/sites/default/default.services.yml
/web/sites/default/default.settings.php
/web/sites/development.services.yml
/web/sites/example.settings.local.php
/web/sites/example.sites.php
/web/themes/README.txt


# Other common rules
# ------------------
# Ignore files generated by PhpStorm and VSCode
.idea/
.vscode/

# Ignore .env files as they are personal
.env
