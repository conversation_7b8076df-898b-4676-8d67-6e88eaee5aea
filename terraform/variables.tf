terraform {
  required_providers {
    random = {
      source  = "hashicorp/random"
      version = "3.6.3"
    }
  }
}
variable "gcp_service_account_key" {
  default = "./gcp.json"
}

variable "gcp_project" {
  description = "GCP Project ID"
}

variable "gcp_region" {
  description = "GCP Region"
  default     = "europe-central2"
}

variable "gcp_drupal_database_name" {
  description = "The Drupal database name"
  default     = "drupal"
}

variable "gcp_drupal_database_user" {
  description = "The Drupal database user name"
  default     = "drupal"
}

variable "image_tag" {
  description = "Tag for the Drupal Apache and PHP images"
  default     = "latest"
}

variable "app_env" {
  description = "Environment for the application (e.g. production, staging, development)"
  default     = "production"
}
