resource "google_sql_database_instance" "drupal_database" {
  name   = "database"
  region = var.gcp_region

  database_version = "MYSQL_8_0"

  settings {
    tier = "db-f1-micro"
  }
}

resource "random_password" "drupal_database_password" {
  length           = 24
  special          = true
  override_special = "!#"
}

resource "google_sql_user" "drupal_user" {
  instance = google_sql_database_instance.drupal_database.name
  name     = var.gcp_drupal_database_user
  password = random_password.drupal_database_password.result
}

resource "google_sql_database" "drupal_database" {
  name     = var.gcp_drupal_database_name
  instance = google_sql_database_instance.drupal_database.name
}
