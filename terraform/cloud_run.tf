resource "google_cloud_run_service" "drupal" {
  name     = "drupal"
  location = var.gcp_region

  template {
    metadata {
      annotations = {
        "run.googleapis.com/cpu-throttling"     = "true"
        "run.googleapis.com/cloudsql-instances" = google_sql_database_instance.drupal_database.connection_name
      }
    }
    spec {

      containers {
        image = "${google_artifact_registry_repository.drupal.location}-docker.pkg.dev/${google_artifact_registry_repository.drupal.project}/${google_artifact_registry_repository.drupal.repository_id}/apache:${var.image_tag}"
        name  = "apache"

        ports {
          container_port = 80
          name           = "http1"
        }

        resources {
          limits = {
            cpu    = "1000m"
            memory = "128Mi"
          }
        }

        startup_probe {
          tcp_socket {
            port = 80
          }
          timeout_seconds   = 240
          period_seconds    = 240
          failure_threshold = 1
        }
      }

      containers {
        image = "${google_artifact_registry_repository.drupal.location}-docker.pkg.dev/${google_artifact_registry_repository.drupal.project}/${google_artifact_registry_repository.drupal.repository_id}/php:${var.image_tag}"
        name  = "php"

        resources {
          limits = {
            cpu    = "100m"
            memory = "128Mi"
          }
        }

        env {
          name  = "MYSQL_HOST"
          value = google_sql_database_instance.drupal_database.connection_name
        }

        env {
          name  = "MYSQL_USER"
          value = var.gcp_drupal_database_user
        }

        env {
          name  = "MYSQL_PASSWORD"
          value = random_password.drupal_database_password.result
        }

        env {
          name  = "MYSQL_DATABASE"
          value = var.gcp_drupal_database_name
        }

        env {
          name  = "MYSQL_PORT"
          value = "3306"
        }

        env {
          name  = "PROJECT_BASE_URL"
          value = "${var.gcp_project}.${var.gcp_region}.r.appspot.com"
        }

        env {
          name  = "TRUSTED_HOST_PATTERNS"
          value = "^${var.gcp_project}\\.${replace(var.gcp_region, "-", "\\-")}\\.r\\.appspot\\.com$"
        }
      }
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }
}

resource "google_cloud_run_service_iam_member" "all_users" {
  service  = google_cloud_run_service.drupal.name
  location = google_cloud_run_service.drupal.location
  role     = "roles/run.invoker"
  member   = "allUsers"
}

output "cloud_run_service_url" {
  description = "URL of the Cloud Run service"
  value       = google_cloud_run_service.drupal.status[0].url
}
