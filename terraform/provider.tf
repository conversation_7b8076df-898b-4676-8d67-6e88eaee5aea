provider "google" {
  credentials = file(var.gcp_service_account_key)
  project     = var.gcp_project
  region      = var.gcp_region
}

resource "google_project_service" "apis" {
  for_each = toset([
    "run.googleapis.com",
    "sqladmin.googleapis.com",
    "compute.googleapis.com",
    "servicenetworking.googleapis.com",
    "vpcaccess.googleapis.com",
    "artifactregistry.googleapis.com"
  ])

  service = each.value
}
