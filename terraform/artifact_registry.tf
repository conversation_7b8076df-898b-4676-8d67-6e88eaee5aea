resource "google_artifact_registry_repository" "drupal" {
  format        = "DOCKER"
  repository_id = "drupal"
  vulnerability_scanning_config {
    enablement_config = "DISABLED"
  }
}
output "artifact_registry_repository_drupal_url" {
  value = "${google_artifact_registry_repository.drupal.location}-docker.pkg.dev/${google_artifact_registry_repository.drupal.project}/${google_artifact_registry_repository.drupal.repository_id}"
}
