<VirtualHost *:80>
    DocumentRoot /srv/app/web/
    ServerName default

    <Directory "/srv/app/web">
        Options FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>

    Include conf/conf.d/common.conf
</VirtualHost>

# Additional example host
# <VirtualHost *:80>
#     DocumentRoot /srv/app/web2/
#     ServerName web2.localhost
#
#     <Directory "/srv/app/web2">
#         Options FollowSymLinks
#         AllowOverride All
#         Require all granted
#     </Directory>
#
#     Include conf/conf.d/common.conf
#</VirtualHost>
