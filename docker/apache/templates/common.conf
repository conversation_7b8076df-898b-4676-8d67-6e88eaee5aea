<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

<FilesMatch "\.(?:sh|sql|mysql|po|tpl|make|test)$">
    Order allow,deny
    Deny from all
</FilesMatch>

<DirectoryMatch "^\.|\/\.">
    Order allow,deny
    Deny from all
</DirectoryMatch>

Protocols h2c http/1.1

DirectoryIndex index.php

<Proxy "fcgi://php:9000/">
    ProxySet connectiontimeout=5 timeout=60
</Proxy>
<FilesMatch "\.php$">
    <If "-f %{REQUEST_FILENAME}">
        SetHandler "proxy:fcgi://php:9000"
    </If>
</FilesMatch>
