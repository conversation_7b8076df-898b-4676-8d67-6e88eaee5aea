# Application related PHP configuration options, used in both CLI and FPM environments.
# Can be overridden for FPM in ../php-fpm.d/zzz-app.conf

apc.enable_cli = 1
date.timezone = UTC
session.auto_start = Off
short_open_tag = Off
memory_limit = -1
session.save_path = /mnt/session

# http://symfony.com/doc/current/performance.html
opcache.enable = 1
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 20000
opcache.memory_consumption = 255
opcache.validate_timestamps = 0
realpath_cache_size = 4096K
realpath_cache_ttl = 600
# opcache.preload_user = www-data
# opcache.preload = /srv/app/config/preload.php
upload_max_filesize = 100M
post_max_size = 100M

# Xdebug
xdebug.mode = debug
xdebug.client_host = host.docker.internal
xdebug.start_with_request = true

# Error reporting
error_reporting = E_ALL & ~E_DEPRECATED
