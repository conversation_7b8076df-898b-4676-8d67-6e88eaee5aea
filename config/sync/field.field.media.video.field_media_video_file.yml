uuid: c46b1948-e8cb-4ae6-916d-0cc780d728e4
langcode: en
status: true
dependencies:
  config:
    - field.storage.media.field_media_video_file
    - media.type.video
  module:
    - content_translation
    - file
third_party_settings:
  content_translation:
    translation_sync:
      target_id: target_id
      display: display
      description: description
id: media.video.field_media_video_file
field_name: field_media_video_file
entity_type: media
bundle: video
label: 'Video file'
description: ''
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: media/video
  file_extensions: mp4
  max_filesize: ''
  description_field: true
field_type: file
