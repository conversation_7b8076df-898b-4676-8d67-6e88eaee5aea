uuid: 50f063cc-a849-46b6-a23a-a3f31ff42ac2
langcode: en
status: true
dependencies:
  config:
    - field.storage.media.field_media_image
    - media.type.image
  module:
    - content_translation
    - image
third_party_settings:
  content_translation:
    translation_sync:
      alt: alt
      title: title
      file: '0'
id: media.image.field_media_image
field_name: field_media_image
entity_type: media
bundle: image
label: Image
description: ''
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: media/image
  file_extensions: 'png gif jpg jpeg webp'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: true
  alt_field_required: true
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
