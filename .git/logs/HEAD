0000000000000000000000000000000000000000 57b84f3f47bd49632b12661daa96a96b224aa8d3 <PERSON><PERSON> <marcin.ma<PERSON><PERSON><PERSON>@isobar.com> 1741789573 +0100	commit (initial): Initial commit
57b84f3f47bd49632b12661daa96a96b224aa8d3 69b5eec9cefb0825f9ab32bc300c99b8e2dfcbff <PERSON><PERSON> <marcin.ma<PERSON><PERSON><PERSON>@isobar.com> 1741789987 +0100	commit (amend): Initial commit
69b5eec9cefb0825f9ab32bc300c99b8e2dfcbff 69b5eec9cefb0825f9ab32bc300c99b8e2dfcbff <PERSON><PERSON> <marcin.marus<PERSON><PERSON>@isobar.com> 1741790330 +0100	checkout: moving from master to initial-commit
69b5eec9cefb0825f9ab32bc300c99b8e2dfcbff 69b5eec9cefb0825f9ab32bc300c99b8e2dfcbff <PERSON><PERSON> <marcin.ma<PERSON><PERSON><PERSON>@isobar.com> 1741791145 +0100	checkout: moving from initial-commit to master
69b5eec9cefb0825f9ab32bc300c99b8e2dfcbff dfd56ec067364fa8fe088497dbc85cfd34c13beb <PERSON>in Maruszewski <<EMAIL>> ********** +0100	commit (amend): Initial commit
dfd56ec067364fa8fe088497dbc85cfd34c13beb dfd56ec067364fa8fe088497dbc85cfd34c13beb Marcin Maruszewski <<EMAIL>> ********** +0100	checkout: moving from master to ai
dfd56ec067364fa8fe088497dbc85cfd34c13beb 7692433cd020d0ed1b127014a3d31f6749442f11 Marcin Maruszewski <<EMAIL>> ********** +0100	commit: Add Drupal AI and Gemini Provider modules to composer.json and composer.lock; update core.extension.yml for new modules
7692433cd020d0ed1b127014a3d31f6749442f11 25e4b5dc4ec1b1a5236b3602f5d898f00bb9a377 Marcin Maruszewski <<EMAIL>> ********** +0100	commit: Update base.sql.gz
25e4b5dc4ec1b1a5236b3602f5d898f00bb9a377 dfd56ec067364fa8fe088497dbc85cfd34c13beb Marcin Maruszewski <<EMAIL>> ********** +0100	checkout: moving from ai to master
dfd56ec067364fa8fe088497dbc85cfd34c13beb 954df379f49f682a39bfa9a858bc1d9173d04530 Marcin Maruszewski <<EMAIL>> ********** +0100	pull: Fast-forward
954df379f49f682a39bfa9a858bc1d9173d04530 954df379f49f682a39bfa9a858bc1d9173d04530 Marcin Maruszewski <<EMAIL>> ********** +0100	checkout: moving from master to group
954df379f49f682a39bfa9a858bc1d9173d04530 954df379f49f682a39bfa9a858bc1d9173d04530 Marcin Maruszewski <<EMAIL>> ********** +0100	reset: moving to HEAD
954df379f49f682a39bfa9a858bc1d9173d04530 954df379f49f682a39bfa9a858bc1d9173d04530 Marcin Maruszewski <<EMAIL>> ********** +0100	checkout: moving from group to master
954df379f49f682a39bfa9a858bc1d9173d04530 dfd56ec067364fa8fe088497dbc85cfd34c13beb Marcin Maruszewski <<EMAIL>> 1752752260 +0200	reset: moving to dfd56ec067364fa8fe088497dbc85cfd34c13beb
dfd56ec067364fa8fe088497dbc85cfd34c13beb 12c1f98fb95e69b496384c4d5bdd98eaf23f6cb9 Marcin Maruszewski <<EMAIL>> 1752778849 +0200	commit (amend): Initial commit.
12c1f98fb95e69b496384c4d5bdd98eaf23f6cb9 0d6bc2426cb5f49988a997524683d641bad71e85 Marcin Maruszewski <<EMAIL>> 1752821990 +0200	commit (amend): Initial commit.
0d6bc2426cb5f49988a997524683d641bad71e85 b278bb0c0be6c1cf945dcbbc477ab4230c91b95c Marcin Maruszewski <<EMAIL>> 1752832740 +0200	commit: Update ignored environment files.
b278bb0c0be6c1cf945dcbbc477ab4230c91b95c efba5f815325c46b42f1a41f3aadd7c4275cab87 Marcin Maruszewski <<EMAIL>> 1753741038 +0200	commit: Initial Drupal setup.
efba5f815325c46b42f1a41f3aadd7c4275cab87 13a93eff3f21124d629abd671609dd90b85ff1ef Marcin Maruszewski <<EMAIL>> 1753741168 +0200	commit (amend): Initial Drupal setup.
13a93eff3f21124d629abd671609dd90b85ff1ef ffdf306b5c9d37fb4297b4e9d7d5b448f178e030 Marcin Maruszewski <<EMAIL>> 1753741215 +0200	commit (amend): Initial Drupal setup.
ffdf306b5c9d37fb4297b4e9d7d5b448f178e030 2ab52db0a03a6c70b8dbedf6f93d2055b16b56a0 Marcin Maruszewski <<EMAIL>> 1753741265 +0200	commit (amend): Initial Drupal setup.
2ab52db0a03a6c70b8dbedf6f93d2055b16b56a0 b278bb0c0be6c1cf945dcbbc477ab4230c91b95c Marcin Maruszewski <<EMAIL>> 1753771973 +0200	reset: moving to b278bb0c0be6c1cf945dcbbc477ab4230c91b95c
b278bb0c0be6c1cf945dcbbc477ab4230c91b95c 0b6bbf55b88bc31ee16c239fd37713a9a136dbe0 Marcin Maruszewski <<EMAIL>> 1753771983 +0200	commit: Initial Drupal setup.
0b6bbf55b88bc31ee16c239fd37713a9a136dbe0 0b6bbf55b88bc31ee16c239fd37713a9a136dbe0 Marcin Maruszewski <<EMAIL>> 1753781801 +0200	checkout: moving from master to terraform
0b6bbf55b88bc31ee16c239fd37713a9a136dbe0 00da7ba802ea32c6144ba8ad70bd291a4755dbbe Marcin Maruszewski <<EMAIL>> 1753786950 +0200	commit: Initial Terraform setup.
00da7ba802ea32c6144ba8ad70bd291a4755dbbe 7d96b698df4972d83ded3e273c37544041034d8c Marcin Maruszewski <<EMAIL>> 1753797552 +0200	commit (amend): Initial Terraform setup.
7d96b698df4972d83ded3e273c37544041034d8c 82eee91bc2492ddeb6c76665abada9bd56f0c2b0 Marcin Maruszewski <<EMAIL>> 1753799124 +0200	commit (amend): Initial Terraform setup.
82eee91bc2492ddeb6c76665abada9bd56f0c2b0 d9a4b1b70cd80bd8ef533853718984b2d0730c63 Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Initial Terraform setup.
d9a4b1b70cd80bd8ef533853718984b2d0730c63 eda39005580d8c2e2a74c6cd3993e4fc2017a85b Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Initial Terraform setup.
eda39005580d8c2e2a74c6cd3993e4fc2017a85b de8c7a46e9d48ec8e5470ca558a50ee0b261d844 Marcin Maruszewski <<EMAIL>> ********** +0200	commit: Add GCP provider.
de8c7a46e9d48ec8e5470ca558a50ee0b261d844 2b35a945ff6f6a9de9875dd6f3c1c4f77abb75c4 Marcin Maruszewski <<EMAIL>> ********** +0200	commit: Create Docker artifact registry for PHP and Apache containers.
2b35a945ff6f6a9de9875dd6f3c1c4f77abb75c4 bfe6ce3fb2945fb53162b8583aa3d9b88f528fb8 Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Create Docker artifact registry for PHP and Apache images.
bfe6ce3fb2945fb53162b8583aa3d9b88f528fb8 69ff40f8a1a5ad4cfe442b8aeef728563cb42655 Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Create Docker artifact registry for PHP and Apache images.
69ff40f8a1a5ad4cfe442b8aeef728563cb42655 2f6d03ce15b8f53dca192af379df980fe0b18522 Marcin Maruszewski <<EMAIL>> ********** +0200	commit: Initialize GitLab pipelines.
2f6d03ce15b8f53dca192af379df980fe0b18522 94f5a58ba20bf606d38566da0e829ba863af95f2 Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Initialize GitLab pipelines.
94f5a58ba20bf606d38566da0e829ba863af95f2 0cc1b924b983878c8c01a8273d5d96354d995532 Marcin Maruszewski <<EMAIL>> 1753818571 +0200	commit (amend): Initialize GitLab pipelines.
0cc1b924b983878c8c01a8273d5d96354d995532 abb454842944ed35f0485b0029b9a1306b437892 Marcin Maruszewski <<EMAIL>> 1753821572 +0200	commit: Working on build script.
abb454842944ed35f0485b0029b9a1306b437892 8d212313f6413d3b44fd661bad7233b352222f3d Marcin Maruszewski <<EMAIL>> 1753821616 +0200	commit (amend): Working on build script.
8d212313f6413d3b44fd661bad7233b352222f3d d19fc6263a298a251e2d83ec7b15b333a8dea853 Marcin Maruszewski <<EMAIL>> 1753821896 +0200	commit (amend): Working on build script.
d19fc6263a298a251e2d83ec7b15b333a8dea853 15f9bf4fc5462b6c925a6933942b8d18ec9faf87 Marcin Maruszewski <<EMAIL>> 1753822003 +0200	commit (amend): Working on build script.
15f9bf4fc5462b6c925a6933942b8d18ec9faf87 e07d94f14b29cf4f659e07d09b3beea08ce48284 Marcin Maruszewski <<EMAIL>> 1753822010 +0200	commit (amend): Working on build script.
e07d94f14b29cf4f659e07d09b3beea08ce48284 574ecf4f4929d5c39a3e4715f8d8b833d4d07bb5 Marcin Maruszewski <<EMAIL>> 1753860940 +0200	commit (amend): Working on build script.
574ecf4f4929d5c39a3e4715f8d8b833d4d07bb5 794d9f1d366e1022b7bb2094a53d15b1ea26919f Marcin Maruszewski <<EMAIL>> 1753862301 +0200	commit (amend): Working on build script.
794d9f1d366e1022b7bb2094a53d15b1ea26919f 8e437238c4afc37dfa1bc9d4c8639e0dac42b204 Marcin Maruszewski <<EMAIL>> 1753862392 +0200	commit (amend): Working on build script.
8e437238c4afc37dfa1bc9d4c8639e0dac42b204 2658dbd8fc090d508099bcc1078fdc38ee100d03 Marcin Maruszewski <<EMAIL>> 1753863054 +0200	commit (amend): Working on build script.
2658dbd8fc090d508099bcc1078fdc38ee100d03 fb802cd76cea5e914ff9245983cd08f2d10e6270 Marcin Maruszewski <<EMAIL>> 1753863145 +0200	commit (amend): Working on build script.
fb802cd76cea5e914ff9245983cd08f2d10e6270 8e4b51702a04970fe20c21491acc75f816f02955 Marcin Maruszewski <<EMAIL>> 1753863188 +0200	commit (amend): Working on build script.
8e4b51702a04970fe20c21491acc75f816f02955 0df89cc06f74b73d58163c2312707e55f5e8163e Marcin Maruszewski <<EMAIL>> 1753863512 +0200	commit (amend): Working on build script.
0df89cc06f74b73d58163c2312707e55f5e8163e 0acee0fa8d0ccd4bd071911bc5bee1d8a4a3e77f Marcin Maruszewski <<EMAIL>> 1753863605 +0200	commit (amend): Working on build script.
0acee0fa8d0ccd4bd071911bc5bee1d8a4a3e77f f91cb195152f8a5e42701726ed815fb798392da9 Marcin Maruszewski <<EMAIL>> 1753863728 +0200	commit (amend): Working on build script.
f91cb195152f8a5e42701726ed815fb798392da9 5070db8048cdff9cd4ba0d05c644f673d82a695e Marcin Maruszewski <<EMAIL>> 1753863822 +0200	commit (amend): Working on build script.
5070db8048cdff9cd4ba0d05c644f673d82a695e 962d03a2bce5e0ad88ccf7ba46a68cc3b5776f61 Marcin Maruszewski <<EMAIL>> 1753863897 +0200	commit (amend): Working on build script.
962d03a2bce5e0ad88ccf7ba46a68cc3b5776f61 fdbd3063d8824c69ad57cc7d81eb357a1e9f9192 Marcin Maruszewski <<EMAIL>> 1753863982 +0200	commit (amend): Working on build script.
fdbd3063d8824c69ad57cc7d81eb357a1e9f9192 231189c7feec6ce157d26966a52215b32125fe0b Marcin Maruszewski <<EMAIL>> 1753864150 +0200	commit (amend): Working on build script.
231189c7feec6ce157d26966a52215b32125fe0b 7b457a7baffdf62828c212a69ac3f6e4180b5134 Marcin Maruszewski <<EMAIL>> 1753864434 +0200	commit (amend): Working on build script.
7b457a7baffdf62828c212a69ac3f6e4180b5134 2dcfdc1f320ff2cd7099093927993b275a8d5649 Marcin Maruszewski <<EMAIL>> 1753864655 +0200	commit (amend): Working on build script.
2dcfdc1f320ff2cd7099093927993b275a8d5649 8ce6659af9a8b41200e3307dc550bab754bb4781 Marcin Maruszewski <<EMAIL>> 1753864694 +0200	commit (amend): Working on build script.
8ce6659af9a8b41200e3307dc550bab754bb4781 9df4060d65d4fb08e3f44653dfc211093f778df0 Marcin Maruszewski <<EMAIL>> 1753868047 +0200	commit (amend): Working on build script.
9df4060d65d4fb08e3f44653dfc211093f778df0 7185e4431c04eff2b1b3e101e211be4828bce955 Marcin Maruszewski <<EMAIL>> 1753868394 +0200	commit (amend): Working on build script.
7185e4431c04eff2b1b3e101e211be4828bce955 3a29569a43fb921ed389fbcc2bd6433b4c784860 Marcin Maruszewski <<EMAIL>> 1753868412 +0200	commit (amend): Working on build script.
3a29569a43fb921ed389fbcc2bd6433b4c784860 6ffdaffc7c9e4599728fd2afee79080976d8c0c7 Marcin Maruszewski <<EMAIL>> 1753868668 +0200	commit (amend): Working on build script.
6ffdaffc7c9e4599728fd2afee79080976d8c0c7 1e50b2c1670e15375ce3fdd11e9881739766fc1c Marcin Maruszewski <<EMAIL>> 1753868769 +0200	commit (amend): Working on build script.
1e50b2c1670e15375ce3fdd11e9881739766fc1c fca57bddbc4cbb79e16cadbd46e49492ead4f7fb Marcin Maruszewski <<EMAIL>> 1753869504 +0200	commit (amend): Working on build script.
fca57bddbc4cbb79e16cadbd46e49492ead4f7fb 5ce4788113deedbaf0606057d9da7620e3336607 Marcin Maruszewski <<EMAIL>> 1753869667 +0200	commit (amend): Working on build script.
5ce4788113deedbaf0606057d9da7620e3336607 35cba1f542fc7e569b306db36adefe775bcf2db5 Marcin Maruszewski <<EMAIL>> 1753870132 +0200	commit (amend): Working on build script.
35cba1f542fc7e569b306db36adefe775bcf2db5 89218545ea890f6a173d066fdb741ea8d68f0b5e Marcin Maruszewski <<EMAIL>> 1753870494 +0200	commit (amend): Working on build script.
89218545ea890f6a173d066fdb741ea8d68f0b5e 667b50053e5e53ea076a708cc545aa42a4464b19 Marcin Maruszewski <<EMAIL>> 1753870538 +0200	commit (amend): Working on build script.
667b50053e5e53ea076a708cc545aa42a4464b19 7e2c793aabd29881e97e1202903d5ddbf26c9f13 Marcin Maruszewski <<EMAIL>> 1753870728 +0200	commit (amend): Working on build script.
7e2c793aabd29881e97e1202903d5ddbf26c9f13 9ba6701a6d3d0ded3137f0d8c00cb66262f25c23 Marcin Maruszewski <<EMAIL>> 1753871443 +0200	commit (amend): Working on build script.
9ba6701a6d3d0ded3137f0d8c00cb66262f25c23 bcb705f873f55b14c1aa7cac42813a9a7edd6c6c Marcin Maruszewski <<EMAIL>> 1753871595 +0200	commit (amend): Working on build script.
bcb705f873f55b14c1aa7cac42813a9a7edd6c6c c78b219fb970f03f7d92a282d1a8230ef304a166 Marcin Maruszewski <<EMAIL>> 1753871874 +0200	commit (amend): Working on build script.
c78b219fb970f03f7d92a282d1a8230ef304a166 6d5f2c131a33954af3686d6c3346e3154214eb41 Marcin Maruszewski <<EMAIL>> 1753881943 +0200	commit: Add database.
6d5f2c131a33954af3686d6c3346e3154214eb41 fefe628aa798f404f9823d5debb053b7a0a3ecb0 Marcin Maruszewski <<EMAIL>> 1753882488 +0200	commit: Upgrade cache policy.
fefe628aa798f404f9823d5debb053b7a0a3ecb0 6bbf0597ec3bb4721da7f39f9dcbc6b053ea853f Marcin Maruszewski <<EMAIL>> 1753883693 +0200	commit (amend): Upgrade cache policy.
6bbf0597ec3bb4721da7f39f9dcbc6b053ea853f 6d8758b58fd1a58e13cf7783f8e8e55966e9b245 Marcin Maruszewski <<EMAIL>> 1753883705 +0200	commit (amend): Upgrade cache policy.
6d8758b58fd1a58e13cf7783f8e8e55966e9b245 9eaf9b36cf3bca640527b088aeda027cf611df45 Marcin Maruszewski <<EMAIL>> 1753884092 +0200	commit (amend): Upgrade cache policy.
9eaf9b36cf3bca640527b088aeda027cf611df45 9eaf9b36cf3bca640527b088aeda027cf611df45 Marcin Maruszewski <<EMAIL>> 1753892256 +0200	reset: moving to HEAD
9eaf9b36cf3bca640527b088aeda027cf611df45 72c15c24b33da182bebdc1b6904e1e1316985644 Marcin Maruszewski <<EMAIL>> 1753894048 +0200	commit: Add Terraform configuration for cloud_run.
72c15c24b33da182bebdc1b6904e1e1316985644 38a7211de56c97cd534cb9de924ce3300813be03 Marcin Maruszewski <<EMAIL>> 1753894073 +0200	commit: Add plan stage.
38a7211de56c97cd534cb9de924ce3300813be03 14e45bb5170523d517177960ab41e7339775cf52 Marcin Maruszewski <<EMAIL>> 1753894356 +0200	commit: Try BUILDKIT_INLINE_CACHE.
14e45bb5170523d517177960ab41e7339775cf52 c559256ed2e44eeb33fbc47e70aef62f6bd9cac2 Marcin Maruszewski <<EMAIL>> 1753897497 +0200	commit (amend): Try BUILDKIT_INLINE_CACHE.
c559256ed2e44eeb33fbc47e70aef62f6bd9cac2 4eac40efbd67c5d739eba4c479af377bedd9c543 Marcin Maruszewski <<EMAIL>> 1753902244 +0200	commit: Update plan stage.
4eac40efbd67c5d739eba4c479af377bedd9c543 3e5fd180fd79d7028c37559e4b27cd37c8810c18 Marcin Maruszewski <<EMAIL>> 1753902864 +0200	commit: Terraform cache updates.
3e5fd180fd79d7028c37559e4b27cd37c8810c18 fe9b4642c688f34ee7e965baab06f0dd4c27e5c5 Marcin Maruszewski <<EMAIL>> 1753903548 +0200	commit: Common variables for all jobs.
fe9b4642c688f34ee7e965baab06f0dd4c27e5c5 620667c114564d593ef70813a9e384d76d748f03 Marcin Maruszewski <<EMAIL>> 1753903692 +0200	commit (amend): Common variables for all jobs.
620667c114564d593ef70813a9e384d76d748f03 2bf59437c6289ddaa24a59bbbde5db9c791484eb Marcin Maruszewski <<EMAIL>> 1753904370 +0200	commit (amend): Common variables for all jobs.
2bf59437c6289ddaa24a59bbbde5db9c791484eb 3596c2ac9f91d828509c7d83a15c7eb6b3b0ba09 Marcin Maruszewski <<EMAIL>> 1753906092 +0200	commit: Temporary disable cloud_run.tf.
3596c2ac9f91d828509c7d83a15c7eb6b3b0ba09 8558d4d639b9542678f3dbdca5237752533f83a1 Marcin Maruszewski <<EMAIL>> 1753906104 +0200	commit: Minor database updates.
8558d4d639b9542678f3dbdca5237752533f83a1 cf29f9b5f444b9ff73f7046c681cdda7f556e442 Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Minor database updates.
cf29f9b5f444b9ff73f7046c681cdda7f556e442 a63722ecf338ae6b85e0f80b41a812c2fde536e7 Marcin Maruszewski <<EMAIL>> ********** +0200	commit: Simplify variables.
a63722ecf338ae6b85e0f80b41a812c2fde536e7 38d15cf1047e453fd73ede58a924ab782ad1f5a7 Marcin Maruszewski <<EMAIL>> ********** +0200	commit: Add cache for terraform providers.
38d15cf1047e453fd73ede58a924ab782ad1f5a7 060cc5a573dc69317a1c0a8d8d9fcfcb246507b4 Marcin Maruszewski <<EMAIL>> ********** +0200	commit: Remove cache.
060cc5a573dc69317a1c0a8d8d9fcfcb246507b4 bc4876a69e4e34c92950a93adc215cea6980fe53 Marcin Maruszewski <<EMAIL>> ********** +0200	commit: Update terraform artifacts.
bc4876a69e4e34c92950a93adc215cea6980fe53 6ce0b27d1ae76882a3c98453643c7864584b8f0d Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Update terraform artifacts.
6ce0b27d1ae76882a3c98453643c7864584b8f0d dcbc0902f4a1c0cf185f5393a4764db6825e5792 Marcin Maruszewski <<EMAIL>> ********** +0200	commit: Simplify terraform init.
dcbc0902f4a1c0cf185f5393a4764db6825e5792 1eebd5f404aca63706a13db2ad5aa2c01654d355 Marcin Maruszewski <<EMAIL>> ********** +0200	commit: Terraform plan updates.
1eebd5f404aca63706a13db2ad5aa2c01654d355 bcdba3ff8ef4a9ca94cf78b34af9fa25dbc0fe99 Marcin Maruszewski <<EMAIL>> ********** +0200	commit (amend): Terraform plan updates.
bcdba3ff8ef4a9ca94cf78b34af9fa25dbc0fe99 69057c12cb60f7c5e27874095e6d644ad9d5551d Marcin Maruszewski <<EMAIL>> 1753912651 +0200	commit: Temporary disable build job.
69057c12cb60f7c5e27874095e6d644ad9d5551d 5671823f46401546ae4fc4b9cadc97d5972e2a81 Marcin Maruszewski <<EMAIL>> 1753913390 +0200	commit: Reconfigure backend.
5671823f46401546ae4fc4b9cadc97d5972e2a81 6f852d760c9200c53641ea1de81d549cebf66396 Marcin Maruszewski <<EMAIL>> 1753917225 +0200	commit: Rename database.
6f852d760c9200c53641ea1de81d549cebf66396 bcdb01ab0cab07cc6910928757bd64866faa4fe1 Marcin Maruszewski <<EMAIL>> 1753919522 +0200	commit: Enable build.
bcdb01ab0cab07cc6910928757bd64866faa4fe1 1d8309203834be42bda7088e9db0852167ecc7da Marcin Maruszewski <<EMAIL>> 1753919687 +0200	commit: Unify variables.
1d8309203834be42bda7088e9db0852167ecc7da 13a305567322035a10de788410329a7fd48467b0 Marcin Maruszewski <<EMAIL>> 1753919799 +0200	commit (amend): Unify variables.
13a305567322035a10de788410329a7fd48467b0 a7876eb187679d08b512f5b683bf0c50f28771ac Marcin Maruszewski <<EMAIL>> 1753954400 +0200	commit: Add apply stage.
a7876eb187679d08b512f5b683bf0c50f28771ac 8e2c5f1bdbad9561d870283d7a595f78a3584b94 Marcin Maruszewski <<EMAIL>> 1753955116 +0200	commit (amend): Add apply stage.
8e2c5f1bdbad9561d870283d7a595f78a3584b94 f7bb500995ca5dbf7a4dd656d59f7a6a2d22d036 Marcin Maruszewski <<EMAIL>> 1753955833 +0200	commit: Include cloud_run configuration.
f7bb500995ca5dbf7a4dd656d59f7a6a2d22d036 d214b3a7e56eaa809b039a0ea849ac8a4dbe91ef Marcin Maruszewski <<EMAIL>> 1753956581 +0200	commit (amend): Include cloud_run configuration.
d214b3a7e56eaa809b039a0ea849ac8a4dbe91ef f734e6162adf49742bf97418246b28810b9d1cd0 Marcin Maruszewski <<EMAIL>> 1753957365 +0200	commit (amend): Include cloud_run configuration.
f734e6162adf49742bf97418246b28810b9d1cd0 370ba28b8b212a83e1644e5eec1c194eb1c94a30 Marcin Maruszewski <<EMAIL>> 1753957389 +0200	commit (amend): Include cloud_run configuration.
370ba28b8b212a83e1644e5eec1c194eb1c94a30 163bbb0aa78b20514eccd2d05952dcac1702c9cf Marcin Maruszewski <<EMAIL>> 1753957444 +0200	commit: Simplify variables.
163bbb0aa78b20514eccd2d05952dcac1702c9cf d3b211522435624b597703aec907bb7c862201aa Marcin Maruszewski <<EMAIL>> 1753957536 +0200	commit: Don't allow apply to fail.
d3b211522435624b597703aec907bb7c862201aa a29b60ae494aa69425668766a6cb7a22270d1064 Marcin Maruszewski <<EMAIL>> 1753960162 +0200	commit: Temporary disable build.
a29b60ae494aa69425668766a6cb7a22270d1064 c8acbd92e7bfacec6da3aece90b1af84e6a51d3f Marcin Maruszewski <<EMAIL>> 1753961297 +0200	commit: Include required APIs.
c8acbd92e7bfacec6da3aece90b1af84e6a51d3f 3d6d98e1a2e3fff354f57e8bb4ca8b608f9fdff6 Marcin Maruszewski <<EMAIL>> 1753964423 +0200	commit: Disable vulnerability scanning for artifact registry.
3d6d98e1a2e3fff354f57e8bb4ca8b608f9fdff6 3d6d98e1a2e3fff354f57e8bb4ca8b608f9fdff6 Marcin Maruszewski <<EMAIL>> 1753968686 +0200	reset: moving to HEAD
3d6d98e1a2e3fff354f57e8bb4ca8b608f9fdff6 3d6d98e1a2e3fff354f57e8bb4ca8b608f9fdff6 Marcin Maruszewski <<EMAIL>> 1754000321 +0200	reset: moving to HEAD
3d6d98e1a2e3fff354f57e8bb4ca8b608f9fdff6 5b4f572534cb5906a5ed9bac24bab23f32a39970 Marcin Maruszewski <<EMAIL>> 1754003421 +0200	commit: Reenable image build.
