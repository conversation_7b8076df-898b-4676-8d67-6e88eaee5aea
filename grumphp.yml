grumphp:
  environment:
    variables:
      GRUMPHP_COMPOSER_DIR: /home/<USER>/app/drupal
  git_hook_variables:
    EXEC_GRUMPHP_COMMAND: [ 'docker-compose', '--env-file', './env/.common.env', 'run', '-T', '--rm', '--no-deps', 'grumphp' ]
  tasks:
    composer:
      file: html/composer.json
    phpstan: null
    phpcs:
      standard:
        - Drupal
        - DrupalPractice
    yamllint:
      parse_custom_tags: true

  testsuites:
    coding_standards:
      tasks:
        - composer
        - phpcs
        - phpstan
        - yamllint

  fixer:
    enabled: false
