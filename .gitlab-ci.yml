default:
  tags:
    - content-factory

variables:
  TF_SERVER_HOST: $CI_SERVER_HOST
  TF_STATE_NAME: dev
  TF_HTTP_USERNAME: "gitlab-ci-token"
  TF_HTTP_PASSWORD: $CI_JOB_TOKEN
  TF_VAR_image_tag: $CI_COMMIT_REF_NAME

stages:
  - init
  - auth
  - build
  - plan
  - apply

init:
  stage: init
  image:
    name: hashicorp/terraform:1.12
    entrypoint: [ "" ]

  before_script:
    - cd terraform
  script:
    - >
      terraform init
      -backend-config="address=https://${TF_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/terraform/state/${TF_STATE_NAME}"
      -backend-config="lock_address=https://${TF_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/terraform/state/${TF_STATE_NAME}/lock"
      -backend-config="unlock_address=https://${TF_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/terraform/state/${TF_STATE_NAME}/lock"
      -backend-config="username=${TF_HTTP_USERNAME}"
      -backend-config="password=${TF_HTTP_PASSWORD}"
    - terraform output -json > terraform_outputs.json
  artifacts:
    paths:
      - terraform
    expire_in: 1 week

docker_auth:
  image: google/cloud-sdk:alpine
  stage: auth
  variables:
    GCP_SERVICE_ACCOUNT_KEY: $TF_VAR_gcp_service_account_key
    GCP_PROJECT_ID: $TF_VAR_gcp_project
  script:
    - gcloud auth activate-service-account --key-file="$GCP_SERVICE_ACCOUNT_KEY"
    - gcloud config set project $GCP_PROJECT_ID
    - gcloud auth print-access-token > docker_access_token.txt
  artifacts:
    paths:
      - docker_access_token.txt
    expire_in: 1 week

build_drupal:
  image: docker:28.3.3
  stage: build
  dependencies:
    - init
    - docker_auth
  services:
    - docker:28.3.3-dind
  variables:
    DOCKER_TLS_CERTDIR: ""
  before_script:
    - apk add jq
    - REPOSITORY_URL=$(jq -r '.artifact_registry_repository_drupal_url.value' terraform/terraform_outputs.json)
    - ACCESS_TOKEN=$(cat docker_access_token.txt)
    - REGISTRY_HOST=$(echo $REPOSITORY_URL | cut -d'/' -f1)
    - echo $ACCESS_TOKEN | docker login -u oauth2accesstoken --password-stdin https://$REGISTRY_HOST
  script:
    - docker pull $REPOSITORY_URL/php:latest || docker pull $REPOSITORY_URL/php:$CI_COMMIT_REF_NAME || true
    - docker pull $REPOSITORY_URL/apache:latest || docker pull $REPOSITORY_URL/apache:$CI_COMMIT_REF_NAME || true
    - docker build -f docker/drupal.Dockerfile --target app_php -t $REPOSITORY_URL/php:$CI_COMMIT_REF_NAME --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $REPOSITORY_URL/php:latest --cache-from $REPOSITORY_URL/php:$CI_COMMIT_REF_NAME .
    - docker build -f docker/drupal.Dockerfile --target apache -t $REPOSITORY_URL/apache:$CI_COMMIT_REF_NAME --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from $REPOSITORY_URL/apache:latest --cache-from $REPOSITORY_URL/apache:$CI_COMMIT_REF_NAME .
    - docker push $REPOSITORY_URL/php:$CI_COMMIT_REF_NAME
    - docker push $REPOSITORY_URL/apache:$CI_COMMIT_REF_NAME

plan:
  stage: plan
  image:
    name: hashicorp/terraform:1.12
    entrypoint: [ "" ]
  dependencies:
    - init
  before_script:
    - cd terraform
    - >
      terraform init -reconfigure
      -backend-config="address=https://${TF_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/terraform/state/${TF_STATE_NAME}"
      -backend-config="lock_address=https://${TF_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/terraform/state/${TF_STATE_NAME}/lock"
      -backend-config="unlock_address=https://${TF_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/terraform/state/${TF_STATE_NAME}/lock"
      -backend-config="username=${TF_HTTP_USERNAME}"
      -backend-config="password=${TF_HTTP_PASSWORD}"
  script:
    - terraform plan

apply:
  stage: apply
  when: manual
  allow_failure: false
  image:
    name: hashicorp/terraform:1.12
    entrypoint: [ "" ]
  before_script:
    - cd terraform
    - >
      terraform init -reconfigure
      -backend-config="address=https://${TF_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/terraform/state/${TF_STATE_NAME}"
      -backend-config="lock_address=https://${TF_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/terraform/state/${TF_STATE_NAME}/lock"
      -backend-config="unlock_address=https://${TF_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/terraform/state/${TF_STATE_NAME}/lock"
      -backend-config="username=${TF_HTTP_USERNAME}"
      -backend-config="password=${TF_HTTP_PASSWORD}"
  script:
    - terraform apply -auto-approve
