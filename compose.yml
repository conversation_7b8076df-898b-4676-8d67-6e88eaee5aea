services:
  php:
    build:
      args:
        USE_XDEBUG: 1
      context: .
      dockerfile: ./docker/drupal.Dockerfile
      target: app_php
    env_file:
      - ./env/.common.env
      - ./env/.php.env
    volumes:
      - ./drupal:/srv/app
      - ./docker/mysql:/srv/mysql
      - ./docker/php/conf.d/symfony.dev.ini:/usr/local/etc/php/conf.d/symfony.ini
    extra_hosts:
      - host.docker.internal:host-gateway

  grumphp:
    build:
      args:
        USE_XDEBUG: 0
      context: .
      dockerfile: ./docker/drupal.Dockerfile
      target: app_php
    working_dir: /home/<USER>/app
    volumes:
      - ./:/home/<USER>/app
      - ./docker/php/conf.d/symfony.dev.ini:/usr/local/etc/php/conf.d/symfony.ini

  apache:
    build:
      args:
        USE_XDEBUG: 1
      context: .
      dockerfile: ./docker/drupal.Dockerfile
      target: apache
    depends_on:
      - php
    env_file:
      - ./env/.common.env
      - ./env/.apache.env
    volumes:
      - ./drupal:/srv/app
    labels:
      - "traefik.enable=true"
      - "traefik.http.services.${PROJECT_NAME}_apache.loadbalancer.server.port=80"
      - "traefik.http.routers.apache.rule=Host(`${PROJECT_BASE_URL}`)"

  mysql:
    image: mysql:${MYSQL_VERSION:-8.0}
    env_file:
      - ./env/.common.env
    command: >
      --max-allowed-packet=16M
      --sql-mode="STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO"
      --transaction-isolation=READ-COMMITTED
    volumes:
      - ./docker/mysql:/docker-entrypoint-initdb.d

  pma:
    image: phpmyadmin/phpmyadmin
    environment:
      PMA_HOST: $MYSQL_HOST
      PMA_USER: root
      PMA_PASSWORD: $MYSQL_ROOT_PASSWORD
      UPLOAD_LIMIT: 1G
    labels:
    - "traefik.enable=true"
    - "traefik.http.services.${PROJECT_NAME}_pma.loadbalancer.server.port=80"
    - "traefik.http.routers.${PROJECT_NAME}_pma.rule=Host(`pma.${PROJECT_BASE_URL}`)"

  mailpit:
    image: axllent/mailpit
    labels:
      - "traefik.enable=true"
      - "traefik.http.services.${PROJECT_NAME}_mailpit.loadbalancer.server.port=8025"
      - "traefik.http.routers.${PROJECT_NAME}_mailpit.rule=Host(`mailpit.${PROJECT_BASE_URL}`)"

  terraform:
    image: hashicorp/terraform:1.12
    init: true
    volumes:
      - ./terraform:/app
    user: '1000'
    working_dir: /app

  traefik:
    image: traefik:v3.0
    command: --api.insecure=true --providers.docker --providers.docker.exposedbydefault=false
    ports:
      - "80:80"
      - '8080:8080' # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
