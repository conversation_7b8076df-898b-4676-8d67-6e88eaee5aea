{"name": "isobar/content-factory", "description": "Content Factory project.", "type": "project", "homepage": "https://gitlab.hyperlab.pl/isobar/content-factory", "require": {"php": "^8.3", "composer/installers": "^2.3", "cweagans/composer-patches": "~1.0", "drupal/better_exposed_filters": "^7.0", "drupal/config_split": "^2.0", "drupal/core-composer-scaffold": "^11.2", "drupal/core-recommended": "^11.2", "drupal/diff": "^2.0@beta", "drupal/easy_breadcrumb": "^2.0", "drupal/entity": "^1.6", "drupal/events_log_track": "^4.0", "drupal/gin": "^5.0", "drupal/gin_login": "^2.1", "drupal/gin_toolbar_custom_menu": "^1.0", "drupal/m4032404": "^2.0@alpha", "drupal/mail_login": "^4.2", "drupal/mailsystem": "^4.5", "drupal/media_directories": "^2.2@beta", "drupal/menu_admin_per_menu": "^1.7", "drupal/module_filter": "^5.0", "drupal/paragraphs": "^1.19", "drupal/pathauto": "^1.13", "drupal/queue_mail": "^1.7", "drupal/queue_ui": "^3.2", "drupal/quick_node_clone": "^1.22", "drupal/redirect": "^1.11", "drupal/search_api": "^1.38", "drupal/search_api_autocomplete": "^1.10", "drupal/search_api_db_defaults": "^1.38", "drupal/smtp": "^1.4", "drupal/ultimate_cron": "^2.0@beta", "drush/drush": "^13.6", "oomphinc/composer-installers-extender": "^2.0", "wikimedia/composer-merge-plugin": "^2.1"}, "require-dev": {"drupal/core-dev": "^11.2", "drupal/devel": "^5.4", "ergebnis/composer-normalize": "^2.47", "phpro/grumphp": "^2.14"}, "conflict": {"drupal/drupal": "*"}, "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}, {"type": "composer", "url": "https://asset-packagist.org"}], "minimum-stability": "dev", "prefer-stable": true, "config": {"allow-plugins": {"composer/installers": true, "cweagans/composer-patches": true, "dealerdirect/phpcodesniffer-composer-installer": true, "drupal/core-composer-scaffold": true, "ergebnis/composer-normalize": true, "oomphinc/composer-installers-extender": true, "php-http/discovery": true, "phpro/grumphp": true, "phpstan/extension-installer": true, "tbachert/spi": true, "wikimedia/composer-merge-plugin": true}, "sort-packages": true}, "extra": {"drupal-scaffold": {"locations": {"web-root": "web/"}}, "grumphp": {"config-default-path": "/home/<USER>/app", "project-path": "/home/<USER>/app"}, "installer-types": ["bower-asset", "npm-asset"], "installer-paths": {"web/core": ["type:drupal-core"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/contrib/{$name}": ["type:drupal-profile"], "web/themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/contrib/{$name}": ["type:drupal-drush"], "web/modules/custom/{$name}": ["type:drupal-custom-module"], "web/profiles/custom/{$name}": ["type:drupal-custom-profile"], "web/themes/custom/{$name}": ["type:drupal-custom-theme"], "web/libraries/ckeditor5-anchor-drupal": ["npm-asset/northernco--ckeditor5-anchor-drupal"], "web/libraries/{$name}": ["type:drupal-library", "type:bower-asset", "type:npm-asset"]}}}