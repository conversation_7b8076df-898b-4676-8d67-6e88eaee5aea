# 🖼️ Content Factory 

**Content Factory** is a tool that leverages Large Language Models to generate engaging social media content based on user-provided briefs.

The tool is built using **Drupal CMF** for content management and **LLM-based orchestrators** for intelligent content generation and workflow automation.

## 📌 TLDR

```shell
task init
```


## Project structure

```shell
.
├── README.md
├── ai
│ └── ... (orchestrators)
├── docker
├── drupal
└── terraform

```
### 🤖 AI 

Contains LLM-based tools responsible for structuring user briefs and generating various types of assets such as images and videos.

### 🐳 Docker 

Contains Dockerfiles and configurations for the project's core services.

### 💙 Drupal

A web application built with Drupal CMF that provides the user interface and content management capabilities.

### ⛰️ Terraform

Infrastructure as Code (IaC) configuration used to provision and manage the deployment environments.

## 🔧 Local project setup

To set up the project locally, make sure you have all required dependencies installed (e.g. Docker, Taskfile support), then run:

```bash
task init
```

This command will initialize the local development environment, including Drupal setup, AI services, and local infrastructure configuration.
